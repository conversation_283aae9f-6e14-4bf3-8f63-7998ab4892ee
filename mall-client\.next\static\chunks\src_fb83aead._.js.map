{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mall/mall-client/src/components/Product/ProductCard.tsx"], "sourcesContent": ["import React from 'react';\nimport Image from 'next/image';\nimport Link from 'next/link';\n\ninterface Product {\n  id: number;\n  name: string;\n  description: string;\n  price: number;\n  stock: number;\n  imageUrl: string;\n}\n\ninterface ProductCardProps {\n  product: Product;\n}\n\nconst ProductCard: React.FC<ProductCardProps> = ({ product }) => {\n  // 處理圖片URL，如果是相對路徑或無效URL，使用占位圖\n  const getImageUrl = (url: string) => {\n    if (!url) return '/placeholder.jpg';\n    \n    // 如果是相對路徑，轉換為絕對路徑\n    if (url.startsWith('/images/')) {\n      return `https://via.placeholder.com/300x200/f0f0f0/666666?text=${encodeURIComponent(product.name)}`;\n    }\n    \n    // 如果是完整URL但可能無效，提供備用\n    try {\n      new URL(url);\n      return url;\n    } catch {\n      return `https://via.placeholder.com/300x200/f0f0f0/666666?text=${encodeURIComponent(product.name)}`;\n    }\n  };\n\n  return (\n    <Link href={`/products/${product.id}`}>\n      <div className=\"border rounded-lg shadow-md p-4 flex flex-col items-center text-center hover:shadow-lg transition-shadow duration-300 cursor-pointer\">\n        <div className=\"relative w-full h-48 mb-4\">\n          <Image\n            src={getImageUrl(product.imageUrl)}\n            alt={product.name}\n            fill\n            style={{ objectFit: \"contain\" }}\n            className=\"rounded-md\"\n            onError={(e) => {\n              // 如果圖片加載失敗，使用占位圖\n              const target = e.target as HTMLImageElement;\n              target.src = '/placeholder.jpg';\n            }}\n          />\n        </div>\n        <h2 className=\"text-lg font-semibold mb-2 line-clamp-2\">{product.name}</h2>\n        <p className=\"text-gray-600 mb-2 text-sm line-clamp-3\">{product.description}</p>\n        <p className=\"text-xl font-bold text-green-600 mb-1\">¥{product.price.toFixed(2)}</p>\n        <p className=\"text-sm text-gray-500\">\n          庫存: {product.stock > 0 ? product.stock : '缺貨'}\n        </p>\n        {product.stock === 0 && (\n          <span className=\"mt-2 px-2 py-1 bg-red-100 text-red-600 text-xs rounded\">\n            暫時缺貨\n          </span>\n        )}\n      </div>\n    </Link>\n  );\n};\n\nexport default ProductCard;\n"], "names": [], "mappings": ";;;;AACA;AACA;;;;AAeA,MAAM,cAA0C,CAAC,EAAE,OAAO,EAAE;IAC1D,8BAA8B;IAC9B,MAAM,cAAc,CAAC;QACnB,IAAI,CAAC,KAAK,OAAO;QAEjB,kBAAkB;QAClB,IAAI,IAAI,UAAU,CAAC,aAAa;YAC9B,OAAO,CAAC,uDAAuD,EAAE,mBAAmB,QAAQ,IAAI,GAAG;QACrG;QAEA,qBAAqB;QACrB,IAAI;YACF,IAAI,IAAI;YACR,OAAO;QACT,EAAE,OAAM;YACN,OAAO,CAAC,uDAAuD,EAAE,mBAAmB,QAAQ,IAAI,GAAG;QACrG;IACF;IAEA,qBACE,6LAAC,+JAAA,CAAA,UAAI;QAAC,MAAM,CAAC,UAAU,EAAE,QAAQ,EAAE,EAAE;kBACnC,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;wBACJ,KAAK,YAAY,QAAQ,QAAQ;wBACjC,KAAK,QAAQ,IAAI;wBACjB,IAAI;wBACJ,OAAO;4BAAE,WAAW;wBAAU;wBAC9B,WAAU;wBACV,SAAS,CAAC;4BACR,iBAAiB;4BACjB,MAAM,SAAS,EAAE,MAAM;4BACvB,OAAO,GAAG,GAAG;wBACf;;;;;;;;;;;8BAGJ,6LAAC;oBAAG,WAAU;8BAA2C,QAAQ,IAAI;;;;;;8BACrE,6LAAC;oBAAE,WAAU;8BAA2C,QAAQ,WAAW;;;;;;8BAC3E,6LAAC;oBAAE,WAAU;;wBAAwC;wBAAE,QAAQ,KAAK,CAAC,OAAO,CAAC;;;;;;;8BAC7E,6LAAC;oBAAE,WAAU;;wBAAwB;wBAC9B,QAAQ,KAAK,GAAG,IAAI,QAAQ,KAAK,GAAG;;;;;;;gBAE1C,QAAQ,KAAK,KAAK,mBACjB,6LAAC;oBAAK,WAAU;8BAAyD;;;;;;;;;;;;;;;;;AAOnF;KAlDM;uCAoDS", "debugId": null}}, {"offset": {"line": 133, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mall/mall-client/src/components/Product/CategoryFilter.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\n\ninterface Category {\n  id: number;\n  name: string;\n  count?: number;\n}\n\ninterface CategoryFilterProps {\n  onCategoryChange: (categoryId: number | null) => void;\n  selectedCategory: number | null;\n}\n\nconst CategoryFilter: React.FC<CategoryFilterProps> = ({ \n  onCategoryChange, \n  selectedCategory \n}) => {\n  const [categories, setCategories] = useState<Category[]>([]);\n  const [loading, setLoading] = useState(true);\n\n  // 模擬分類數據，實際項目中會從API獲取\n  useEffect(() => {\n    const fetchCategories = async () => {\n      try {\n        // 這裡應該調用後端API獲取分類\n        // const response = await fetch('http://localhost:8080/api/categories');\n        // const data = await response.json();\n        \n        // 模擬數據\n        const mockCategories: Category[] = [\n          { id: 1, name: '電子產品', count: 15 },\n          { id: 2, name: '服裝配飾', count: 8 },\n          { id: 3, name: '家居用品', count: 12 },\n          { id: 4, name: '運動戶外', count: 6 },\n          { id: 5, name: '美妝護理', count: 9 },\n        ];\n        \n        setCategories(mockCategories);\n      } catch (error) {\n        console.error('獲取分類失敗:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchCategories();\n  }, []);\n\n  if (loading) {\n    return (\n      <div className=\"mb-6\">\n        <h3 className=\"text-lg font-semibold mb-3\">商品分類</h3>\n        <div className=\"flex flex-wrap gap-2\">\n          {[1, 2, 3, 4, 5].map((i) => (\n            <div key={i} className=\"h-8 w-20 bg-gray-200 rounded animate-pulse\"></div>\n          ))}\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"mb-6\">\n      <h3 className=\"text-lg font-semibold mb-3\">商品分類</h3>\n      <div className=\"flex flex-wrap gap-2\">\n        <button\n          onClick={() => onCategoryChange(null)}\n          className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${\n            selectedCategory === null\n              ? 'bg-blue-500 text-white'\n              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'\n          }`}\n        >\n          全部\n        </button>\n        {categories.map((category) => (\n          <button\n            key={category.id}\n            onClick={() => onCategoryChange(category.id)}\n            className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${\n              selectedCategory === category.id\n                ? 'bg-blue-500 text-white'\n                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'\n            }`}\n          >\n            {category.name}\n            {category.count && (\n              <span className=\"ml-1 text-xs opacity-75\">({category.count})</span>\n            )}\n          </button>\n        ))}\n      </div>\n    </div>\n  );\n};\n\nexport default CategoryFilter;\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAeA,MAAM,iBAAgD,CAAC,EACrD,gBAAgB,EAChB,gBAAgB,EACjB;;IACC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IAC3D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,sBAAsB;IACtB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM;4DAAkB;oBACtB,IAAI;wBACF,kBAAkB;wBAClB,wEAAwE;wBACxE,sCAAsC;wBAEtC,OAAO;wBACP,MAAM,iBAA6B;4BACjC;gCAAE,IAAI;gCAAG,MAAM;gCAAQ,OAAO;4BAAG;4BACjC;gCAAE,IAAI;gCAAG,MAAM;gCAAQ,OAAO;4BAAE;4BAChC;gCAAE,IAAI;gCAAG,MAAM;gCAAQ,OAAO;4BAAG;4BACjC;gCAAE,IAAI;gCAAG,MAAM;gCAAQ,OAAO;4BAAE;4BAChC;gCAAE,IAAI;gCAAG,MAAM;gCAAQ,OAAO;4BAAE;yBACjC;wBAED,cAAc;oBAChB,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,WAAW;oBAC3B,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;QACF;mCAAG,EAAE;IAEL,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAA6B;;;;;;8BAC3C,6LAAC;oBAAI,WAAU;8BACZ;wBAAC;wBAAG;wBAAG;wBAAG;wBAAG;qBAAE,CAAC,GAAG,CAAC,CAAC,kBACpB,6LAAC;4BAAY,WAAU;2BAAb;;;;;;;;;;;;;;;;IAKpB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;0BAA6B;;;;;;0BAC3C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,SAAS,IAAM,iBAAiB;wBAChC,WAAW,CAAC,6DAA6D,EACvE,qBAAqB,OACjB,2BACA,+CACJ;kCACH;;;;;;oBAGA,WAAW,GAAG,CAAC,CAAC,yBACf,6LAAC;4BAEC,SAAS,IAAM,iBAAiB,SAAS,EAAE;4BAC3C,WAAW,CAAC,6DAA6D,EACvE,qBAAqB,SAAS,EAAE,GAC5B,2BACA,+CACJ;;gCAED,SAAS,IAAI;gCACb,SAAS,KAAK,kBACb,6LAAC;oCAAK,WAAU;;wCAA0B;wCAAE,SAAS,KAAK;wCAAC;;;;;;;;2BAVxD,SAAS,EAAE;;;;;;;;;;;;;;;;;AAiB5B;GAjFM;KAAA;uCAmFS", "debugId": null}}, {"offset": {"line": 306, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mall/mall-client/src/app/products/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport ProductCard from '../../components/Product/ProductCard';\nimport CategoryFilter from '../../components/Product/CategoryFilter';\n\ninterface Product {\n  id: number;\n  name: string;\n  description: string;\n  price: number;\n  stock: number;\n  imageUrl: string;\n  categoryId?: number;\n}\n\nexport default function ProductsPage() {\n  const [products, setProducts] = useState<Product[]>([]);\n  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);\n  const [selectedCategory, setSelectedCategory] = useState<number | null>(null);\n  const [sortBy, setSortBy] = useState<string>('default');\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  // 獲取商品數據\n  useEffect(() => {\n    const fetchProducts = async () => {\n      try {\n        setLoading(true);\n        const response = await fetch(\"http://localhost:8080/api/products\");\n        if (!response.ok) {\n          throw new Error(\"Failed to fetch products\");\n        }\n        const data = await response.json();\n        setProducts(data);\n        setFilteredProducts(data);\n      } catch (err: unknown) {\n        if (err instanceof Error) {\n          setError(err.message);\n        } else {\n          setError(String(err));\n        }\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchProducts();\n  }, []);\n\n  // 處理分類篩選\n  useEffect(() => {\n    let filtered = selectedCategory === null \n      ? products \n      : products.filter(product => product.categoryId === selectedCategory);\n\n    // 排序\n    switch (sortBy) {\n      case 'price-low':\n        filtered = [...filtered].sort((a, b) => a.price - b.price);\n        break;\n      case 'price-high':\n        filtered = [...filtered].sort((a, b) => b.price - a.price);\n        break;\n      case 'name':\n        filtered = [...filtered].sort((a, b) => a.name.localeCompare(b.name));\n        break;\n      default:\n        // 保持原順序\n        break;\n    }\n\n    setFilteredProducts(filtered);\n  }, [selectedCategory, products, sortBy]);\n\n  const handleCategoryChange = (categoryId: number | null) => {\n    setSelectedCategory(categoryId);\n  };\n\n  const handleSortChange = (event: React.ChangeEvent<HTMLSelectElement>) => {\n    setSortBy(event.target.value);\n  };\n\n  if (loading) {\n    return (\n      <div className=\"container mx-auto px-4 py-8\">\n        <div className=\"text-center py-8\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto\"></div>\n          <p className=\"mt-4 text-gray-600\">載入中...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"container mx-auto px-4 py-8\">\n        <div className=\"text-red-500 text-center py-8\">\n          <p>錯誤: {error}</p>\n          <button \n            onClick={() => window.location.reload()} \n            className=\"mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600\"\n          >\n            重新載入\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"container mx-auto px-4 py-8\">\n      {/* 頁面標題 */}\n      <div className=\"mb-6\">\n        <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">所有商品</h1>\n        <p className=\"text-gray-600\">探索我們精選的商品系列</p>\n      </div>\n\n      {/* 分類篩選 */}\n      <CategoryFilter \n        onCategoryChange={handleCategoryChange}\n        selectedCategory={selectedCategory}\n      />\n\n      {/* 排序和結果統計 */}\n      <div className=\"flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4\">\n        <div className=\"text-sm text-gray-600\">\n          共找到 {filteredProducts.length} 件商品\n          {selectedCategory && (\n            <button\n              onClick={() => setSelectedCategory(null)}\n              className=\"ml-2 text-blue-600 hover:text-blue-800\"\n            >\n              清除篩選\n            </button>\n          )}\n        </div>\n        \n        <div className=\"flex items-center space-x-2\">\n          <label htmlFor=\"sort\" className=\"text-sm text-gray-700\">排序:</label>\n          <select\n            id=\"sort\"\n            value={sortBy}\n            onChange={handleSortChange}\n            className=\"border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500\"\n          >\n            <option value=\"default\">默認排序</option>\n            <option value=\"price-low\">價格由低到高</option>\n            <option value=\"price-high\">價格由高到低</option>\n            <option value=\"name\">按名稱排序</option>\n          </select>\n        </div>\n      </div>\n\n      {/* 商品列表 */}\n      {filteredProducts.length === 0 ? (\n        <div className=\"text-center py-12\">\n          <div className=\"text-gray-400 mb-4\">\n            <svg className=\"mx-auto h-12 w-12\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8V4a1 1 0 00-1-1H7a1 1 0 00-1 1v1m8 0V4.5\" />\n            </svg>\n          </div>\n          <h3 className=\"text-lg font-medium text-gray-900 mb-2\">\n            {selectedCategory ? '該分類暫無商品' : '暫無商品'}\n          </h3>\n          <p className=\"text-gray-500\">\n            {selectedCategory ? '請嘗試其他分類或清除篩選條件' : '請稍後再試'}\n          </p>\n        </div>\n      ) : (\n        <div className=\"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6\">\n          {filteredProducts.map((product) => (\n            <ProductCard key={product.id} product={product} />\n          ))}\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAgBe,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACxE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,SAAS;IACT,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM;wDAAgB;oBACpB,IAAI;wBACF,WAAW;wBACX,MAAM,WAAW,MAAM,MAAM;wBAC7B,IAAI,CAAC,SAAS,EAAE,EAAE;4BAChB,MAAM,IAAI,MAAM;wBAClB;wBACA,MAAM,OAAO,MAAM,SAAS,IAAI;wBAChC,YAAY;wBACZ,oBAAoB;oBACtB,EAAE,OAAO,KAAc;wBACrB,IAAI,eAAe,OAAO;4BACxB,SAAS,IAAI,OAAO;wBACtB,OAAO;4BACL,SAAS,OAAO;wBAClB;oBACF,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;QACF;iCAAG,EAAE;IAEL,SAAS;IACT,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,WAAW,qBAAqB,OAChC,WACA,SAAS,MAAM;0CAAC,CAAA,UAAW,QAAQ,UAAU,KAAK;;YAEtD,KAAK;YACL,OAAQ;gBACN,KAAK;oBACH,WAAW;2BAAI;qBAAS,CAAC,IAAI;kDAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;;oBACzD;gBACF,KAAK;oBACH,WAAW;2BAAI;qBAAS,CAAC,IAAI;kDAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;;oBACzD;gBACF,KAAK;oBACH,WAAW;2BAAI;qBAAS,CAAC,IAAI;kDAAC,CAAC,GAAG,IAAM,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,IAAI;;oBACnE;gBACF;oBAEE;YACJ;YAEA,oBAAoB;QACtB;iCAAG;QAAC;QAAkB;QAAU;KAAO;IAEvC,MAAM,uBAAuB,CAAC;QAC5B,oBAAoB;IACtB;IAEA,MAAM,mBAAmB,CAAC;QACxB,UAAU,MAAM,MAAM,CAAC,KAAK;IAC9B;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;;;;;;IAI1C;IAEA,IAAI,OAAO;QACT,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;4BAAE;4BAAK;;;;;;;kCACR,6LAAC;wBACC,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;wBACrC,WAAU;kCACX;;;;;;;;;;;;;;;;;IAMT;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;0BAI/B,6LAAC,kJAAA,CAAA,UAAc;gBACb,kBAAkB;gBAClB,kBAAkB;;;;;;0BAIpB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;4BAAwB;4BAChC,iBAAiB,MAAM;4BAAC;4BAC5B,kCACC,6LAAC;gCACC,SAAS,IAAM,oBAAoB;gCACnC,WAAU;0CACX;;;;;;;;;;;;kCAML,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAM,SAAQ;gCAAO,WAAU;0CAAwB;;;;;;0CACxD,6LAAC;gCACC,IAAG;gCACH,OAAO;gCACP,UAAU;gCACV,WAAU;;kDAEV,6LAAC;wCAAO,OAAM;kDAAU;;;;;;kDACxB,6LAAC;wCAAO,OAAM;kDAAY;;;;;;kDAC1B,6LAAC;wCAAO,OAAM;kDAAa;;;;;;kDAC3B,6LAAC;wCAAO,OAAM;kDAAO;;;;;;;;;;;;;;;;;;;;;;;;YAM1B,iBAAiB,MAAM,KAAK,kBAC3B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;4BAAoB,MAAK;4BAAO,SAAQ;4BAAY,QAAO;sCACxE,cAAA,6LAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;;;;;;kCAGzE,6LAAC;wBAAG,WAAU;kCACX,mBAAmB,YAAY;;;;;;kCAElC,6LAAC;wBAAE,WAAU;kCACV,mBAAmB,mBAAmB;;;;;;;;;;;qCAI3C,6LAAC;gBAAI,WAAU;0BACZ,iBAAiB,GAAG,CAAC,CAAC,wBACrB,6LAAC,+IAAA,CAAA,UAAW;wBAAkB,SAAS;uBAArB,QAAQ,EAAE;;;;;;;;;;;;;;;;AAMxC;GAlKwB;KAAA", "debugId": null}}]}