spring.application.name=ecommerce
spring.datasource.url=****************************************************************
spring.datasource.username=root
spring.datasource.password=root
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
mybatis.mapper-locations=classpath:mapper/*.xml
mybatis.configuration.map-underscore-to-camel-case=true

# DevTools exclude static and templates resources from restart
spring.devtools.restart.exclude=static/**,templates/**
spring.devtools.restart.additional-exclude=src/main/resources/**,test/**,build/**,.git/**

# DevTools 调试配置
logging.level.org.springframework.boot.devtools=DEBUG
spring.devtools.restart.poll-interval=2s
spring.devtools.restart.quiet-period=1s
