{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mall/mall-client/src/components/Layout/Header.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState } from 'react';\r\nimport Link from 'next/link';\r\nimport { useRouter } from 'next/navigation';\r\nimport { MagnifyingGlassIcon, Bars3Icon } from '@heroicons/react/24/outline';\r\n\r\nconst Header: React.FC = () => {\r\n  const [searchQuery, setSearchQuery] = useState('');\r\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\r\n  const router = useRouter();\r\n\r\n  const handleSearch = (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    if (searchQuery.trim()) {\r\n      router.push(`/search?q=${encodeURIComponent(searchQuery.trim())}`);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <header\r\n      className=\"bg-gray-800 text-white p-4 shadow-md\"\r\n      style={{\r\n        backgroundColor: '#1f2937',\r\n        color: 'white',\r\n        padding: '1rem',\r\n        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',\r\n        minHeight: '60px'\r\n      }}\r\n    >\r\n      <div className=\"container mx-auto\">\r\n        <div className=\"flex justify-between items-center\" style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\r\n          {/* Logo */}\r\n          <Link href=\"/\" className=\"text-2xl font-bold\">\r\n            E-commerce\r\n          </Link>\r\n\r\n          {/* 搜索框 - 桌面版 */}\r\n          <div className=\"hidden md:flex flex-1 max-w-md mx-8\">\r\n            <form onSubmit={handleSearch} className=\"w-full\">\r\n              <div className=\"relative\">\r\n                <input\r\n                  type=\"text\"\r\n                  placeholder=\"搜索商品...\"\r\n                  value={searchQuery}\r\n                  onChange={(e) => setSearchQuery(e.target.value)}\r\n                  className=\"w-full px-4 py-2 pl-10 text-gray-900 bg-white border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n                />\r\n                <MagnifyingGlassIcon className=\"absolute left-3 top-2.5 h-5 w-5 text-gray-400\" />\r\n              </div>\r\n            </form>\r\n          </div>\r\n\r\n          {/* 導航菜單 - 桌面版 */}\r\n          <nav className=\"hidden md:flex space-x-4\">\r\n            <Link href=\"/\" className=\"hover:text-gray-300\">首頁</Link>\r\n            <Link href=\"/products\" className=\"hover:text-gray-300\">商品</Link>\r\n            <Link href=\"/cart\" className=\"hover:text-gray-300\">購物車</Link>\r\n            <Link href=\"/profile\" className=\"hover:text-gray-300\">我的</Link>\r\n          </nav>\r\n\r\n          {/* 移動端菜單按鈕 */}\r\n          <div className=\"md:hidden\">\r\n            <button\r\n              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\r\n              className=\"text-white focus:outline-none\"\r\n            >\r\n              <Bars3Icon className=\"w-6 h-6\" />\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        {/* 移動端菜單 */}\r\n        {isMobileMenuOpen && (\r\n          <div className=\"md:hidden mt-4 pb-4\">\r\n            {/* 移動端搜索框 */}\r\n            <form onSubmit={handleSearch} className=\"mb-4\">\r\n              <div className=\"relative\">\r\n                <input\r\n                  type=\"text\"\r\n                  placeholder=\"搜索商品...\"\r\n                  value={searchQuery}\r\n                  onChange={(e) => setSearchQuery(e.target.value)}\r\n                  className=\"w-full px-4 py-2 pl-10 text-gray-900 bg-white border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n                />\r\n                <MagnifyingGlassIcon className=\"absolute left-3 top-2.5 h-5 w-5 text-gray-400\" />\r\n              </div>\r\n            </form>\r\n\r\n            {/* 移動端導航 */}\r\n            <nav className=\"flex flex-col space-y-2\">\r\n              <Link href=\"/\" className=\"block py-2 hover:text-gray-300\">首頁</Link>\r\n              <Link href=\"/products\" className=\"block py-2 hover:text-gray-300\">商品</Link>\r\n              <Link href=\"/cart\" className=\"block py-2 hover:text-gray-300\">購物車</Link>\r\n              <Link href=\"/profile\" className=\"block py-2 hover:text-gray-300\">我的</Link>\r\n            </nav>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </header>\r\n  );\r\n};\r\n\r\nexport default Header;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AALA;;;;;;AAOA,MAAM,SAAmB;IACvB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,IAAI,YAAY,IAAI,IAAI;YACtB,OAAO,IAAI,CAAC,CAAC,UAAU,EAAE,mBAAmB,YAAY,IAAI,KAAK;QACnE;IACF;IAEA,qBACE,8OAAC;QACC,WAAU;QACV,OAAO;YACL,iBAAiB;YACjB,OAAO;YACP,SAAS;YACT,WAAW;YACX,WAAW;QACb;kBAEA,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;oBAAoC,OAAO;wBAAE,SAAS;wBAAQ,gBAAgB;wBAAiB,YAAY;oBAAS;;sCAEjI,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;sCAAqB;;;;;;sCAK9C,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAK,UAAU;gCAAc,WAAU;0CACtC,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;4CAC9C,WAAU;;;;;;sDAEZ,8OAAC,qOAAA,CAAA,sBAAmB;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;sCAMrC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAsB;;;;;;8CAC/C,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAY,WAAU;8CAAsB;;;;;;8CACvD,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAQ,WAAU;8CAAsB;;;;;;8CACnD,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAW,WAAU;8CAAsB;;;;;;;;;;;;sCAIxD,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,SAAS,IAAM,oBAAoB,CAAC;gCACpC,WAAU;0CAEV,cAAA,8OAAC,iNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;gBAM1B,kCACC,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAK,UAAU;4BAAc,WAAU;sCACtC,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wCAC9C,WAAU;;;;;;kDAEZ,8OAAC,qOAAA,CAAA,sBAAmB;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAKnC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAiC;;;;;;8CAC1D,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAY,WAAU;8CAAiC;;;;;;8CAClE,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAQ,WAAU;8CAAiC;;;;;;8CAC9D,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAW,WAAU;8CAAiC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO/E;uCAEe", "debugId": null}}]}