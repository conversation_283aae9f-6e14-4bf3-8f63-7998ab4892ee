{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mall/mall-client/src/app/products/%5Bid%5D/page.tsx"], "sourcesContent": ["import React from 'react';\nimport Image from 'next/image';\nimport Link from 'next/link';\nimport { notFound } from 'next/navigation';\n\ninterface Product {\n  id: number;\n  name: string;\n  description: string;\n  price: number;\n  stock: number;\n  imageUrl: string;\n  category?: string;\n  specifications?: string[];\n}\n\ninterface ProductPageProps {\n  params: {\n    id: string;\n  };\n}\n\n// 獲取單個商品數據\nasync function getProduct(id: string): Promise<Product | null> {\n  try {\n    const response = await fetch(`http://localhost:8080/api/products/${id}`, {\n      cache: 'no-store' // 確保獲取最新數據\n    });\n    \n    if (!response.ok) {\n      return null;\n    }\n    \n    const product = await response.json();\n    return product;\n  } catch (error) {\n    console.error('獲取商品詳情失敗:', error);\n    return null;\n  }\n}\n\n// 處理圖片URL\nconst getImageUrl = (url: string, productName: string) => {\n  if (!url) return '/placeholder.jpg';\n  \n  if (url.startsWith('/images/')) {\n    return `https://via.placeholder.com/600x400/f0f0f0/666666?text=${encodeURIComponent(productName)}`;\n  }\n  \n  try {\n    new URL(url);\n    return url;\n  } catch {\n    return `https://via.placeholder.com/600x400/f0f0f0/666666?text=${encodeURIComponent(productName)}`;\n  }\n};\n\nexport default async function ProductPage({ params }: ProductPageProps) {\n  const product = await getProduct(params.id);\n\n  if (!product) {\n    notFound();\n  }\n\n  return (\n    <div className=\"container mx-auto px-4 py-8\">\n      {/* 麵包屑導航 */}\n      <nav className=\"mb-6\">\n        <ol className=\"flex items-center space-x-2 text-sm text-gray-500\">\n          <li>\n            <Link href=\"/\" className=\"hover:text-blue-600\">首頁</Link>\n          </li>\n          <li>/</li>\n          <li>\n            <Link href=\"/products\" className=\"hover:text-blue-600\">商品</Link>\n          </li>\n          <li>/</li>\n          <li className=\"text-gray-900\">{product.name}</li>\n        </ol>\n      </nav>\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n        {/* 商品圖片 */}\n        <div className=\"space-y-4\">\n          <div className=\"relative aspect-square w-full max-w-lg mx-auto\">\n            <Image\n              src={getImageUrl(product.imageUrl, product.name)}\n              alt={product.name}\n              fill\n              style={{ objectFit: \"contain\" }}\n              className=\"rounded-lg border\"\n              priority\n            />\n          </div>\n        </div>\n\n        {/* 商品信息 */}\n        <div className=\"space-y-6\">\n          <div>\n            <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">{product.name}</h1>\n            {product.category && (\n              <p className=\"text-sm text-gray-500 mb-4\">分類: {product.category}</p>\n            )}\n          </div>\n\n          <div className=\"border-t border-b py-4\">\n            <p className=\"text-4xl font-bold text-green-600\">¥{product.price.toFixed(2)}</p>\n          </div>\n\n          <div>\n            <h3 className=\"text-lg font-semibold mb-2\">商品描述</h3>\n            <p className=\"text-gray-700 leading-relaxed\">{product.description}</p>\n          </div>\n\n          {product.specifications && product.specifications.length > 0 && (\n            <div>\n              <h3 className=\"text-lg font-semibold mb-2\">商品規格</h3>\n              <ul className=\"list-disc list-inside space-y-1 text-gray-700\">\n                {product.specifications.map((spec, index) => (\n                  <li key={index}>{spec}</li>\n                ))}\n              </ul>\n            </div>\n          )}\n\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center space-x-2\">\n              <span className=\"text-gray-700\">庫存:</span>\n              <span className={`font-semibold ${product.stock > 0 ? 'text-green-600' : 'text-red-600'}`}>\n                {product.stock > 0 ? `${product.stock} 件` : '缺貨'}\n              </span>\n            </div>\n\n            <div className=\"flex space-x-4\">\n              <button\n                disabled={product.stock === 0}\n                className={`flex-1 py-3 px-6 rounded-lg font-semibold transition-colors ${\n                  product.stock > 0\n                    ? 'bg-blue-600 text-white hover:bg-blue-700'\n                    : 'bg-gray-300 text-gray-500 cursor-not-allowed'\n                }`}\n              >\n                {product.stock > 0 ? '加入購物車' : '暫時缺貨'}\n              </button>\n              \n              <button\n                disabled={product.stock === 0}\n                className={`flex-1 py-3 px-6 rounded-lg font-semibold transition-colors ${\n                  product.stock > 0\n                    ? 'bg-orange-600 text-white hover:bg-orange-700'\n                    : 'bg-gray-300 text-gray-500 cursor-not-allowed'\n                }`}\n              >\n                {product.stock > 0 ? '立即購買' : '暫時缺貨'}\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* 返回按鈕 */}\n      <div className=\"mt-8\">\n        <Link\n          href=\"/\"\n          className=\"inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50\"\n        >\n          ← 返回商品列表\n        </Link>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAAA;;;;;AAmBA,WAAW;AACX,eAAe,WAAW,EAAU;IAClC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,CAAC,mCAAmC,EAAE,IAAI,EAAE;YACvE,OAAO,WAAW,WAAW;QAC/B;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,OAAO;QACT;QAEA,MAAM,UAAU,MAAM,SAAS,IAAI;QACnC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,aAAa;QAC3B,OAAO;IACT;AACF;AAEA,UAAU;AACV,MAAM,cAAc,CAAC,KAAa;IAChC,IAAI,CAAC,KAAK,OAAO;IAEjB,IAAI,IAAI,UAAU,CAAC,aAAa;QAC9B,OAAO,CAAC,uDAAuD,EAAE,mBAAmB,cAAc;IACpG;IAEA,IAAI;QACF,IAAI,IAAI;QACR,OAAO;IACT,EAAE,OAAM;QACN,OAAO,CAAC,uDAAuD,EAAE,mBAAmB,cAAc;IACpG;AACF;AAEe,eAAe,YAAY,EAAE,MAAM,EAAoB;IACpE,MAAM,UAAU,MAAM,WAAW,OAAO,EAAE;IAE1C,IAAI,CAAC,SAAS;QACZ,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAG,WAAU;;sCACZ,8OAAC;sCACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;0CAAsB;;;;;;;;;;;sCAEjD,8OAAC;sCAAG;;;;;;sCACJ,8OAAC;sCACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAY,WAAU;0CAAsB;;;;;;;;;;;sCAEzD,8OAAC;sCAAG;;;;;;sCACJ,8OAAC;4BAAG,WAAU;sCAAiB,QAAQ,IAAI;;;;;;;;;;;;;;;;;0BAI/C,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;gCACJ,KAAK,YAAY,QAAQ,QAAQ,EAAE,QAAQ,IAAI;gCAC/C,KAAK,QAAQ,IAAI;gCACjB,IAAI;gCACJ,OAAO;oCAAE,WAAW;gCAAU;gCAC9B,WAAU;gCACV,QAAQ;;;;;;;;;;;;;;;;kCAMd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAyC,QAAQ,IAAI;;;;;;oCAClE,QAAQ,QAAQ,kBACf,8OAAC;wCAAE,WAAU;;4CAA6B;4CAAK,QAAQ,QAAQ;;;;;;;;;;;;;0CAInE,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;;wCAAoC;wCAAE,QAAQ,KAAK,CAAC,OAAO,CAAC;;;;;;;;;;;;0CAG3E,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,8OAAC;wCAAE,WAAU;kDAAiC,QAAQ,WAAW;;;;;;;;;;;;4BAGlE,QAAQ,cAAc,IAAI,QAAQ,cAAc,CAAC,MAAM,GAAG,mBACzD,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,8OAAC;wCAAG,WAAU;kDACX,QAAQ,cAAc,CAAC,GAAG,CAAC,CAAC,MAAM,sBACjC,8OAAC;0DAAgB;+CAAR;;;;;;;;;;;;;;;;0CAMjB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAChC,8OAAC;gDAAK,WAAW,CAAC,cAAc,EAAE,QAAQ,KAAK,GAAG,IAAI,mBAAmB,gBAAgB;0DACtF,QAAQ,KAAK,GAAG,IAAI,GAAG,QAAQ,KAAK,CAAC,EAAE,CAAC,GAAG;;;;;;;;;;;;kDAIhD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,UAAU,QAAQ,KAAK,KAAK;gDAC5B,WAAW,CAAC,4DAA4D,EACtE,QAAQ,KAAK,GAAG,IACZ,6CACA,gDACJ;0DAED,QAAQ,KAAK,GAAG,IAAI,UAAU;;;;;;0DAGjC,8OAAC;gDACC,UAAU,QAAQ,KAAK,KAAK;gDAC5B,WAAW,CAAC,4DAA4D,EACtE,QAAQ,KAAK,GAAG,IACZ,iDACA,gDACJ;0DAED,QAAQ,KAAK,GAAG,IAAI,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQxC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oBACH,MAAK;oBACL,WAAU;8BACX;;;;;;;;;;;;;;;;;AAMT", "debugId": null}}]}