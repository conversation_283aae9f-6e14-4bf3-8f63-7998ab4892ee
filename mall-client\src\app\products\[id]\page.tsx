import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { notFound } from 'next/navigation';

interface Product {
  id: number;
  name: string;
  description: string;
  price: number;
  stock: number;
  imageUrl: string;
  category?: string;
  specifications?: string[];
}

interface ProductPageProps {
  params: {
    id: string;
  };
}

// 獲取單個商品數據
async function getProduct(id: string): Promise<Product | null> {
  try {
    const response = await fetch(`http://localhost:8080/api/products/${id}`, {
      cache: 'no-store' // 確保獲取最新數據
    });
    
    if (!response.ok) {
      return null;
    }
    
    const product = await response.json();
    return product;
  } catch (error) {
    console.error('獲取商品詳情失敗:', error);
    return null;
  }
}

// 處理圖片URL
const getImageUrl = (url: string, productName: string) => {
  if (!url) return '/placeholder.jpg';
  
  if (url.startsWith('/images/')) {
    return `https://via.placeholder.com/600x400/f0f0f0/666666?text=${encodeURIComponent(productName)}`;
  }
  
  try {
    new URL(url);
    return url;
  } catch {
    return `https://via.placeholder.com/600x400/f0f0f0/666666?text=${encodeURIComponent(productName)}`;
  }
};

export default async function ProductPage({ params }: ProductPageProps) {
  const product = await getProduct(params.id);

  if (!product) {
    notFound();
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* 麵包屑導航 */}
      <nav className="mb-6">
        <ol className="flex items-center space-x-2 text-sm text-gray-500">
          <li>
            <Link href="/" className="hover:text-blue-600">首頁</Link>
          </li>
          <li>/</li>
          <li>
            <Link href="/products" className="hover:text-blue-600">商品</Link>
          </li>
          <li>/</li>
          <li className="text-gray-900">{product.name}</li>
        </ol>
      </nav>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* 商品圖片 */}
        <div className="space-y-4">
          <div className="relative aspect-square w-full max-w-lg mx-auto">
            <Image
              src={getImageUrl(product.imageUrl, product.name)}
              alt={product.name}
              fill
              style={{ objectFit: "contain" }}
              className="rounded-lg border"
              priority
            />
          </div>
        </div>

        {/* 商品信息 */}
        <div className="space-y-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">{product.name}</h1>
            {product.category && (
              <p className="text-sm text-gray-500 mb-4">分類: {product.category}</p>
            )}
          </div>

          <div className="border-t border-b py-4">
            <p className="text-4xl font-bold text-green-600">¥{product.price.toFixed(2)}</p>
          </div>

          <div>
            <h3 className="text-lg font-semibold mb-2">商品描述</h3>
            <p className="text-gray-700 leading-relaxed">{product.description}</p>
          </div>

          {product.specifications && product.specifications.length > 0 && (
            <div>
              <h3 className="text-lg font-semibold mb-2">商品規格</h3>
              <ul className="list-disc list-inside space-y-1 text-gray-700">
                {product.specifications.map((spec, index) => (
                  <li key={index}>{spec}</li>
                ))}
              </ul>
            </div>
          )}

          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <span className="text-gray-700">庫存:</span>
              <span className={`font-semibold ${product.stock > 0 ? 'text-green-600' : 'text-red-600'}`}>
                {product.stock > 0 ? `${product.stock} 件` : '缺貨'}
              </span>
            </div>

            <div className="flex space-x-4">
              <button
                disabled={product.stock === 0}
                className={`flex-1 py-3 px-6 rounded-lg font-semibold transition-colors ${
                  product.stock > 0
                    ? 'bg-blue-600 text-white hover:bg-blue-700'
                    : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                }`}
              >
                {product.stock > 0 ? '加入購物車' : '暫時缺貨'}
              </button>
              
              <button
                disabled={product.stock === 0}
                className={`flex-1 py-3 px-6 rounded-lg font-semibold transition-colors ${
                  product.stock > 0
                    ? 'bg-orange-600 text-white hover:bg-orange-700'
                    : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                }`}
              >
                {product.stock > 0 ? '立即購買' : '暫時缺貨'}
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* 返回按鈕 */}
      <div className="mt-8">
        <Link
          href="/"
          className="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
        >
          ← 返回商品列表
        </Link>
      </div>
    </div>
  );
}
