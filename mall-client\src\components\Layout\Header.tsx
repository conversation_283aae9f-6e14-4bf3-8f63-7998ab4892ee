'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { MagnifyingGlassIcon, Bars3Icon } from '@heroicons/react/24/outline';

const Header: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const router = useRouter();

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      router.push(`/search?q=${encodeURIComponent(searchQuery.trim())}`);
    }
  };

  return (
    <header
      className="bg-gray-800 text-white p-4 shadow-md"
      style={{
        backgroundColor: '#1f2937',
        color: 'white',
        padding: '1rem',
        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
        minHeight: '60px'
      }}
    >
      <div className="container mx-auto">
        <div className="flex justify-between items-center" style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          {/* Logo */}
          <Link href="/" className="text-2xl font-bold">
            E-commerce
          </Link>

          {/* 搜索框 - 桌面版 */}
          <div className="hidden md:flex flex-1 max-w-md mx-8">
            <form onSubmit={handleSearch} className="w-full">
              <div className="relative">
                <input
                  type="text"
                  placeholder="搜索商品..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full px-4 py-2 pl-10 text-gray-900 bg-white border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <MagnifyingGlassIcon className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />
              </div>
            </form>
          </div>

          {/* 導航菜單 - 桌面版 */}
          <nav className="hidden md:flex space-x-4">
            <Link href="/" className="hover:text-gray-300">首頁</Link>
            <Link href="/products" className="hover:text-gray-300">商品</Link>
            <Link href="/cart" className="hover:text-gray-300">購物車</Link>
            <Link href="/profile" className="hover:text-gray-300">我的</Link>
          </nav>

          {/* 移動端菜單按鈕 */}
          <div className="md:hidden">
            <button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="text-white focus:outline-none"
            >
              <Bars3Icon className="w-6 h-6" />
            </button>
          </div>
        </div>

        {/* 移動端菜單 */}
        {isMobileMenuOpen && (
          <div className="md:hidden mt-4 pb-4">
            {/* 移動端搜索框 */}
            <form onSubmit={handleSearch} className="mb-4">
              <div className="relative">
                <input
                  type="text"
                  placeholder="搜索商品..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full px-4 py-2 pl-10 text-gray-900 bg-white border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <MagnifyingGlassIcon className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />
              </div>
            </form>

            {/* 移動端導航 */}
            <nav className="flex flex-col space-y-2">
              <Link href="/" className="block py-2 hover:text-gray-300">首頁</Link>
              <Link href="/products" className="block py-2 hover:text-gray-300">商品</Link>
              <Link href="/cart" className="block py-2 hover:text-gray-300">購物車</Link>
              <Link href="/profile" className="block py-2 hover:text-gray-300">我的</Link>
            </nav>
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;