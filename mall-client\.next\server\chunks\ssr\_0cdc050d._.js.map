{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mall/mall-client/src/components/Layout/Header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Layout/Header.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Layout/Header.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAoS,GACjU,kEACA", "debugId": null}}, {"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mall/mall-client/src/components/Layout/Header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Layout/Header.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Layout/Header.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAgR,GAC7S,8CACA", "debugId": null}}, {"offset": {"line": 35, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 45, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mall/mall-client/src/components/Layout/Footer.tsx"], "sourcesContent": ["import React from 'react';\r\n\r\nconst Footer: React.FC = () => {\r\n  return (\r\n    <footer\r\n      className=\"bg-gray-800 text-white p-4 mt-8\"\r\n      style={{\r\n        backgroundColor: '#1f2937',\r\n        color: 'white',\r\n        padding: '1rem',\r\n        marginTop: '2rem',\r\n        minHeight: '60px'\r\n      }}\r\n    >\r\n      <div className=\"container mx-auto text-center\" style={{ textAlign: 'center' }}>\r\n        <p>&copy; {new Date().getFullYear()} E-commerce. All rights reserved.</p>\r\n        <p className=\"mt-2 text-sm\">Powered by Next.js & Tailwind CSS</p>\r\n      </div>\r\n    </footer>\r\n  );\r\n};\r\n\r\nexport default Footer;"], "names": [], "mappings": ";;;;;AAEA,MAAM,SAAmB;IACvB,qBACE,8OAAC;QACC,WAAU;QACV,OAAO;YACL,iBAAiB;YACjB,OAAO;YACP,SAAS;YACT,WAAW;YACX,WAAW;QACb;kBAEA,cAAA,8OAAC;YAAI,WAAU;YAAgC,OAAO;gBAAE,WAAW;YAAS;;8BAC1E,8OAAC;;wBAAE;wBAAQ,IAAI,OAAO,WAAW;wBAAG;;;;;;;8BACpC,8OAAC;oBAAE,WAAU;8BAAe;;;;;;;;;;;;;;;;;AAIpC;uCAEe", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mall/mall-client/src/app/layout.tsx"], "sourcesContent": ["import type { Metadata } from \"next\";\nimport \"./globals.css\";\nimport Header from \"../components/Layout/Header\";\nimport Footer from \"../components/Layout/Footer\";\n\nexport const metadata: Metadata = {\n  title: \"Create Next App\",\n  description: \"Generated by create next app\",\n};\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html lang=\"en\">\n      <body className=\"antialiased\">\n        <div className=\"flex flex-col min-h-screen\">\n          <Header />\n          <main className=\"flex-grow container mx-auto p-4\">\n            {children}\n          </main>\n          <Footer />\n        </div>\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;;;;;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;AACf;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,8OAAC;QAAK,MAAK;kBACT,cAAA,8OAAC;YAAK,WAAU;sBACd,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,sIAAA,CAAA,UAAM;;;;;kCACP,8OAAC;wBAAK,WAAU;kCACb;;;;;;kCAEH,8OAAC,sIAAA,CAAA,UAAM;;;;;;;;;;;;;;;;;;;;;AAKjB", "debugId": null}}, {"offset": {"line": 167, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mall/mall-client/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-rsc'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}]}