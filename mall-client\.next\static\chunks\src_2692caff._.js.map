{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mall/mall-client/src/components/Product/ProductCard.tsx"], "sourcesContent": ["import React from 'react';\nimport Image from 'next/image';\nimport Link from 'next/link';\n\ninterface Product {\n  id: number;\n  name: string;\n  description: string;\n  price: number;\n  stock: number;\n  imageUrl: string;\n}\n\ninterface ProductCardProps {\n  product: Product;\n}\n\nconst ProductCard: React.FC<ProductCardProps> = ({ product }) => {\n  // 處理圖片URL，如果是相對路徑或無效URL，使用占位圖\n  const getImageUrl = (url: string) => {\n    if (!url) return '/placeholder.jpg';\n    \n    // 如果是相對路徑，轉換為絕對路徑\n    if (url.startsWith('/images/')) {\n      return `https://via.placeholder.com/300x200/f0f0f0/666666?text=${encodeURIComponent(product.name)}`;\n    }\n    \n    // 如果是完整URL但可能無效，提供備用\n    try {\n      new URL(url);\n      return url;\n    } catch {\n      return `https://via.placeholder.com/300x200/f0f0f0/666666?text=${encodeURIComponent(product.name)}`;\n    }\n  };\n\n  return (\n    <Link href={`/products/${product.id}`}>\n      <div className=\"border rounded-lg shadow-md p-4 flex flex-col items-center text-center hover:shadow-lg transition-shadow duration-300 cursor-pointer\">\n        <div className=\"relative w-full h-48 mb-4\">\n          <Image\n            src={getImageUrl(product.imageUrl)}\n            alt={product.name}\n            fill\n            style={{ objectFit: \"contain\" }}\n            className=\"rounded-md\"\n            onError={(e) => {\n              // 如果圖片加載失敗，使用占位圖\n              const target = e.target as HTMLImageElement;\n              target.src = '/placeholder.jpg';\n            }}\n          />\n        </div>\n        <h2 className=\"text-lg font-semibold mb-2 line-clamp-2\">{product.name}</h2>\n        <p className=\"text-gray-600 mb-2 text-sm line-clamp-3\">{product.description}</p>\n        <p className=\"text-xl font-bold text-green-600 mb-1\">¥{product.price.toFixed(2)}</p>\n        <p className=\"text-sm text-gray-500\">\n          庫存: {product.stock > 0 ? product.stock : '缺貨'}\n        </p>\n        {product.stock === 0 && (\n          <span className=\"mt-2 px-2 py-1 bg-red-100 text-red-600 text-xs rounded\">\n            暫時缺貨\n          </span>\n        )}\n      </div>\n    </Link>\n  );\n};\n\nexport default ProductCard;\n"], "names": [], "mappings": ";;;;AACA;AACA;;;;AAeA,MAAM,cAA0C,CAAC,EAAE,OAAO,EAAE;IAC1D,8BAA8B;IAC9B,MAAM,cAAc,CAAC;QACnB,IAAI,CAAC,KAAK,OAAO;QAEjB,kBAAkB;QAClB,IAAI,IAAI,UAAU,CAAC,aAAa;YAC9B,OAAO,CAAC,uDAAuD,EAAE,mBAAmB,QAAQ,IAAI,GAAG;QACrG;QAEA,qBAAqB;QACrB,IAAI;YACF,IAAI,IAAI;YACR,OAAO;QACT,EAAE,OAAM;YACN,OAAO,CAAC,uDAAuD,EAAE,mBAAmB,QAAQ,IAAI,GAAG;QACrG;IACF;IAEA,qBACE,6LAAC,+JAAA,CAAA,UAAI;QAAC,MAAM,CAAC,UAAU,EAAE,QAAQ,EAAE,EAAE;kBACnC,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;wBACJ,KAAK,YAAY,QAAQ,QAAQ;wBACjC,KAAK,QAAQ,IAAI;wBACjB,IAAI;wBACJ,OAAO;4BAAE,WAAW;wBAAU;wBAC9B,WAAU;wBACV,SAAS,CAAC;4BACR,iBAAiB;4BACjB,MAAM,SAAS,EAAE,MAAM;4BACvB,OAAO,GAAG,GAAG;wBACf;;;;;;;;;;;8BAGJ,6LAAC;oBAAG,WAAU;8BAA2C,QAAQ,IAAI;;;;;;8BACrE,6LAAC;oBAAE,WAAU;8BAA2C,QAAQ,WAAW;;;;;;8BAC3E,6LAAC;oBAAE,WAAU;;wBAAwC;wBAAE,QAAQ,KAAK,CAAC,OAAO,CAAC;;;;;;;8BAC7E,6LAAC;oBAAE,WAAU;;wBAAwB;wBAC9B,QAAQ,KAAK,GAAG,IAAI,QAAQ,KAAK,GAAG;;;;;;;gBAE1C,QAAQ,KAAK,KAAK,mBACjB,6LAAC;oBAAK,WAAU;8BAAyD;;;;;;;;;;;;;;;;;AAOnF;KAlDM;uCAoDS", "debugId": null}}, {"offset": {"line": 133, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mall/mall-client/src/components/Product/CategoryFilter.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\n\ninterface Category {\n  id: number;\n  name: string;\n  count?: number;\n}\n\ninterface CategoryFilterProps {\n  onCategoryChange: (categoryId: number | null) => void;\n  selectedCategory: number | null;\n}\n\nconst CategoryFilter: React.FC<CategoryFilterProps> = ({ \n  onCategoryChange, \n  selectedCategory \n}) => {\n  const [categories, setCategories] = useState<Category[]>([]);\n  const [loading, setLoading] = useState(true);\n\n  // 模擬分類數據，實際項目中會從API獲取\n  useEffect(() => {\n    const fetchCategories = async () => {\n      try {\n        // 這裡應該調用後端API獲取分類\n        // const response = await fetch('http://localhost:8080/api/categories');\n        // const data = await response.json();\n        \n        // 模擬數據\n        const mockCategories: Category[] = [\n          { id: 1, name: '電子產品', count: 15 },\n          { id: 2, name: '服裝配飾', count: 8 },\n          { id: 3, name: '家居用品', count: 12 },\n          { id: 4, name: '運動戶外', count: 6 },\n          { id: 5, name: '美妝護理', count: 9 },\n        ];\n        \n        setCategories(mockCategories);\n      } catch (error) {\n        console.error('獲取分類失敗:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchCategories();\n  }, []);\n\n  if (loading) {\n    return (\n      <div className=\"mb-6\">\n        <h3 className=\"text-lg font-semibold mb-3\">商品分類</h3>\n        <div className=\"flex flex-wrap gap-2\">\n          {[1, 2, 3, 4, 5].map((i) => (\n            <div key={i} className=\"h-8 w-20 bg-gray-200 rounded animate-pulse\"></div>\n          ))}\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"mb-6\">\n      <h3 className=\"text-lg font-semibold mb-3\">商品分類</h3>\n      <div className=\"flex flex-wrap gap-2\">\n        <button\n          onClick={() => onCategoryChange(null)}\n          className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${\n            selectedCategory === null\n              ? 'bg-blue-500 text-white'\n              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'\n          }`}\n        >\n          全部\n        </button>\n        {categories.map((category) => (\n          <button\n            key={category.id}\n            onClick={() => onCategoryChange(category.id)}\n            className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${\n              selectedCategory === category.id\n                ? 'bg-blue-500 text-white'\n                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'\n            }`}\n          >\n            {category.name}\n            {category.count && (\n              <span className=\"ml-1 text-xs opacity-75\">({category.count})</span>\n            )}\n          </button>\n        ))}\n      </div>\n    </div>\n  );\n};\n\nexport default CategoryFilter;\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAeA,MAAM,iBAAgD,CAAC,EACrD,gBAAgB,EAChB,gBAAgB,EACjB;;IACC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IAC3D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,sBAAsB;IACtB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM;4DAAkB;oBACtB,IAAI;wBACF,kBAAkB;wBAClB,wEAAwE;wBACxE,sCAAsC;wBAEtC,OAAO;wBACP,MAAM,iBAA6B;4BACjC;gCAAE,IAAI;gCAAG,MAAM;gCAAQ,OAAO;4BAAG;4BACjC;gCAAE,IAAI;gCAAG,MAAM;gCAAQ,OAAO;4BAAE;4BAChC;gCAAE,IAAI;gCAAG,MAAM;gCAAQ,OAAO;4BAAG;4BACjC;gCAAE,IAAI;gCAAG,MAAM;gCAAQ,OAAO;4BAAE;4BAChC;gCAAE,IAAI;gCAAG,MAAM;gCAAQ,OAAO;4BAAE;yBACjC;wBAED,cAAc;oBAChB,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,WAAW;oBAC3B,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;QACF;mCAAG,EAAE;IAEL,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAA6B;;;;;;8BAC3C,6LAAC;oBAAI,WAAU;8BACZ;wBAAC;wBAAG;wBAAG;wBAAG;wBAAG;qBAAE,CAAC,GAAG,CAAC,CAAC,kBACpB,6LAAC;4BAAY,WAAU;2BAAb;;;;;;;;;;;;;;;;IAKpB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;0BAA6B;;;;;;0BAC3C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,SAAS,IAAM,iBAAiB;wBAChC,WAAW,CAAC,6DAA6D,EACvE,qBAAqB,OACjB,2BACA,+CACJ;kCACH;;;;;;oBAGA,WAAW,GAAG,CAAC,CAAC,yBACf,6LAAC;4BAEC,SAAS,IAAM,iBAAiB,SAAS,EAAE;4BAC3C,WAAW,CAAC,6DAA6D,EACvE,qBAAqB,SAAS,EAAE,GAC5B,2BACA,+CACJ;;gCAED,SAAS,IAAI;gCACb,SAAS,KAAK,kBACb,6LAAC;oCAAK,WAAU;;wCAA0B;wCAAE,SAAS,KAAK;wCAAC;;;;;;;;2BAVxD,SAAS,EAAE;;;;;;;;;;;;;;;;;AAiB5B;GAjFM;KAAA;uCAmFS", "debugId": null}}, {"offset": {"line": 306, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mall/mall-client/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport ProductCard from '../components/Product/ProductCard';\nimport CategoryFilter from '../components/Product/CategoryFilter';\n\n// 定义 Product 接口\ninterface Product {\n  id: number;\n  name: string;\n  description: string;\n  price: number;\n  stock: number;\n  imageUrl: string;\n  categoryId?: number;\n}\n\nexport default function Home() {\n  const [products, setProducts] = useState<Product[]>([]);\n  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);\n  const [selectedCategory, setSelectedCategory] = useState<number | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  // 獲取商品數據\n  useEffect(() => {\n    const fetchProducts = async () => {\n      try {\n        setLoading(true);\n        const response = await fetch(\"http://localhost:8080/api/products\");\n        if (!response.ok) {\n          throw new Error(\"Failed to fetch products\");\n        }\n        const data = await response.json();\n        setProducts(data);\n        setFilteredProducts(data);\n      } catch (err: unknown) {\n        if (err instanceof Error) {\n          setError(err.message);\n        } else {\n          setError(String(err));\n        }\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchProducts();\n  }, []);\n\n  // 處理分類篩選\n  useEffect(() => {\n    if (selectedCategory === null) {\n      setFilteredProducts(products);\n    } else {\n      const filtered = products.filter(product => product.categoryId === selectedCategory);\n      setFilteredProducts(filtered);\n    }\n  }, [selectedCategory, products]);\n\n  const handleCategoryChange = (categoryId: number | null) => {\n    setSelectedCategory(categoryId);\n  };\n\n  if (loading) {\n    return (\n      <div className=\"container mx-auto px-4 py-8\">\n        <div className=\"text-center py-8\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto\"></div>\n          <p className=\"mt-4 text-gray-600\">載入中...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"container mx-auto px-4 py-8\">\n        <div className=\"text-red-500 text-center py-8\">\n          <p>錯誤: {error}</p>\n          <button\n            onClick={() => window.location.reload()}\n            className=\"mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600\"\n          >\n            重新載入\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"container mx-auto px-4 py-8\">\n      {/* 分類篩選 */}\n      <CategoryFilter\n        onCategoryChange={handleCategoryChange}\n        selectedCategory={selectedCategory}\n      />\n\n      {/* 商品列表 */}\n      {filteredProducts.length === 0 ? (\n        <div className=\"text-center py-8\">\n          <p className=\"text-gray-600\">\n            {selectedCategory ? '該分類暫無商品' : '暫無商品'}\n          </p>\n        </div>\n      ) : (\n        <>\n          <div className=\"mb-4 text-sm text-gray-600\">\n            共找到 {filteredProducts.length} 件商品\n          </div>\n          <div className=\"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6\">\n            {filteredProducts.map((product) => (\n              <ProductCard key={product.id} product={product} />\n            ))}\n          </div>\n        </>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAiBe,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACxE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,SAAS;IACT,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,MAAM;gDAAgB;oBACpB,IAAI;wBACF,WAAW;wBACX,MAAM,WAAW,MAAM,MAAM;wBAC7B,IAAI,CAAC,SAAS,EAAE,EAAE;4BAChB,MAAM,IAAI,MAAM;wBAClB;wBACA,MAAM,OAAO,MAAM,SAAS,IAAI;wBAChC,YAAY;wBACZ,oBAAoB;oBACtB,EAAE,OAAO,KAAc;wBACrB,IAAI,eAAe,OAAO;4BACxB,SAAS,IAAI,OAAO;wBACtB,OAAO;4BACL,SAAS,OAAO;wBAClB;oBACF,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;QACF;yBAAG,EAAE;IAEL,SAAS;IACT,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,IAAI,qBAAqB,MAAM;gBAC7B,oBAAoB;YACtB,OAAO;gBACL,MAAM,WAAW,SAAS,MAAM;+CAAC,CAAA,UAAW,QAAQ,UAAU,KAAK;;gBACnE,oBAAoB;YACtB;QACF;yBAAG;QAAC;QAAkB;KAAS;IAE/B,MAAM,uBAAuB,CAAC;QAC5B,oBAAoB;IACtB;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;;;;;;IAI1C;IAEA,IAAI,OAAO;QACT,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;4BAAE;4BAAK;;;;;;;kCACR,6LAAC;wBACC,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;wBACrC,WAAU;kCACX;;;;;;;;;;;;;;;;;IAMT;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,kJAAA,CAAA,UAAc;gBACb,kBAAkB;gBAClB,kBAAkB;;;;;;YAInB,iBAAiB,MAAM,KAAK,kBAC3B,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAE,WAAU;8BACV,mBAAmB,YAAY;;;;;;;;;;qCAIpC;;kCACE,6LAAC;wBAAI,WAAU;;4BAA6B;4BACrC,iBAAiB,MAAM;4BAAC;;;;;;;kCAE/B,6LAAC;wBAAI,WAAU;kCACZ,iBAAiB,GAAG,CAAC,CAAC,wBACrB,6LAAC,+IAAA,CAAA,UAAW;gCAAkB,SAAS;+BAArB,QAAQ,EAAE;;;;;;;;;;;;;;;;;;AAO1C;GAvGwB;KAAA", "debugId": null}}]}