package io.github.roshad.ecommerce.auth;

import org.springframework.stereotype.Service;

@Service
public class UserServiceImpl implements UserService {

    private final UserMapper userMapper;

    public UserServiceImpl(UserMapper userMapper) {
        this.userMapper = userMapper;
    }

    @Override
    public Long getUserId(String username) {
        User user = userMapper.findByUsername(username);
        return user != null ? user.getId() : null;
    }
    @Override
    public User getUserByUsername(String username) {
        return userMapper.findByUsername(username);
    }
}