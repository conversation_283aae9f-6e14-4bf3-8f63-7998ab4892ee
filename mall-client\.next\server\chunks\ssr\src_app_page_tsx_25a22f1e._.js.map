{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mall/mall-client/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport ProductCard from '../components/Product/ProductCard';\nimport CategoryFilter from '../components/Product/CategoryFilter';\n\n// 定义 Product 接口\ninterface Product {\n  id: number;\n  name: string;\n  description: string;\n  price: number;\n  stock: number;\n  imageUrl: string;\n  categoryId?: number;\n}\n\n// 模拟获取商品数据，实际项目中会调用后端 API\nasync function getProducts(): Promise<Product[]> {\n  // 假设后端 API 运行在 http://localhost:8080/api/products\n  // 请确保后端服务已启动\n  const response = await fetch(\"http://localhost:8080/api/products\");\n  if (!response.ok) {\n    throw new Error(\"Failed to fetch products\");\n  }\n  const data = await response.json();\n  return data;\n}\n\nexport default async function Home() {\n  let products: Product[] = [];\n  let error: string | null = null;\n\n  try {\n    products = await getProducts();\n  } catch (err: unknown) {\n    if (err instanceof Error) {\n        error = err.message;\n    } else {\n        error = String(err);\n    }\n  }\n\n  if (error) {\n    return <div className=\"text-red-500 text-center py-8\">Error: {error}</div>;\n  }\n\n  if (products.length === 0) {\n    return <div className=\"text-center py-8\">暂无商品。</div>;\n  }\n\n  return (\n    <div className=\"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 p-4\">\n      {products.map((product) => (\n        <div key={product.id} className=\"border rounded-lg shadow-md p-4 flex flex-col items-center text-center\">\n          <div className=\"relative w-full h-48 mb-4\">\n            <Image\n              src={product.imageUrl || \"/placeholder.jpg\"} // 使用占位图\n              alt={product.name}\n              fill\n              style={{ objectFit: \"contain\" }}\n              className=\"rounded-md\"\n            />\n          </div>\n          <h2 className=\"text-lg font-semibold mb-2\">{product.name}</h2>\n          <p className=\"text-gray-600 mb-2\">{product.description}</p>\n          <p className=\"text-xl font-bold text-green-600\">¥{product.price.toFixed(2)}</p>\n          <p className=\"text-sm text-gray-500\">库存: {product.stock}</p>\n          <button className=\"mt-4 bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded-full transition duration-300\">\n            加入购物车\n          </button>\n        </div>\n      ))}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAiBA,0BAA0B;AAC1B,eAAe;IACb,kDAAkD;IAClD,aAAa;IACb,MAAM,WAAW,MAAM,MAAM;IAC7B,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,IAAI,MAAM;IAClB;IACA,MAAM,OAAO,MAAM,SAAS,IAAI;IAChC,OAAO;AACT;AAEe,eAAe;IAC5B,IAAI,WAAsB,EAAE;IAC5B,IAAI,QAAuB;IAE3B,IAAI;QACF,WAAW,MAAM;IACnB,EAAE,OAAO,KAAc;QACrB,IAAI,eAAe,OAAO;YACtB,QAAQ,IAAI,OAAO;QACvB,OAAO;YACH,QAAQ,OAAO;QACnB;IACF;IAEA,IAAI,OAAO;QACT,qBAAO,8OAAC;YAAI,WAAU;;gBAAgC;gBAAQ;;;;;;;IAChE;IAEA,IAAI,SAAS,MAAM,KAAK,GAAG;QACzB,qBAAO,8OAAC;YAAI,WAAU;sBAAmB;;;;;;IAC3C;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACZ,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;gBAAqB,WAAU;;kCAC9B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,KAAK,QAAQ,QAAQ,IAAI;4BACzB,KAAK,QAAQ,IAAI;4BACjB,IAAI;4BACJ,OAAO;gCAAE,WAAW;4BAAU;4BAC9B,WAAU;;;;;;;;;;;kCAGd,8OAAC;wBAAG,WAAU;kCAA8B,QAAQ,IAAI;;;;;;kCACxD,8OAAC;wBAAE,WAAU;kCAAsB,QAAQ,WAAW;;;;;;kCACtD,8OAAC;wBAAE,WAAU;;4BAAmC;4BAAE,QAAQ,KAAK,CAAC,OAAO,CAAC;;;;;;;kCACxE,8OAAC;wBAAE,WAAU;;4BAAwB;4BAAK,QAAQ,KAAK;;;;;;;kCACvD,8OAAC;wBAAO,WAAU;kCAAyG;;;;;;;eAdnH,QAAQ,EAAE;;;;;;;;;;AAqB5B", "debugId": null}}]}