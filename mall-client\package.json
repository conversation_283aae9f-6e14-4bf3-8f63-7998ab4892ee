{"name": "mall-client", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@heroicons/react": "^2.2.0", "@tailwindcss/postcss": "^4.1.8", "@tanstack/react-query": "^5.80.6", "autoprefixer": "^10.4.21", "next": "15.3.3", "postcss": "^8.5.4", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwindcss": "^4.1.8", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "typescript": "^5"}}